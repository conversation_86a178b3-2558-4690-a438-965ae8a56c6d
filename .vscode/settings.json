{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "editor.defaultFormatter": "biomejs.biome-nightly", "editor.tabSize": 2, "cSpell.words": ["admitad", "Affalliances", "meilisearch", "qwickcilver", "storecategories", "Trackier"], "workbench.colorCustomizations": {"activityBar.activeBackground": "#2f7c47", "activityBar.background": "#2f7c47", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#422c74", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#2f7c47", "statusBar.background": "#215732", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#2f7c47", "statusBarItem.remoteBackground": "#215732", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#215732", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#21573299", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.remoteColor": "#215732", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "denoland.vscode-deno"}}