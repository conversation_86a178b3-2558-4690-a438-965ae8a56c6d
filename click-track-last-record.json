{"runInfo": {"startTime": "2025-07-09T22:39:00.930Z", "endTime": null, "totalProcessed": 0, "totalCreated": 0, "totalSkipped": 0, "runId": "RUN-1752100767466", "processingDurationMs": 0, "processingDurationSeconds": 0}, "summary": {"conversionsProcessed": 3, "earningsCreated": 0, "earningsWithClick": 0, "earningsWithoutClick": 0, "duplicatesSkipped": 2, "duplicatesByOrderId": 2, "duplicatesByClickId": 0, "missingIdentifiers": 0, "generatedOrderIds": 0, "errorsEncountered": 1, "successRate": "0.00%", "clickMatchRate": "0%"}, "newlyTrackedEarnings": [], "skippedConversions": [{"scenario": 1, "scenarioName": "Duplicate Prevention", "orderId": "12282.6365.338384", "clickId": "CBCLKZQVTL4HCNJ", "partner": "Impact", "reason": "Order ID 12282.6365.338384 already processed - Scenario 1 duplicate prevention", "duplicateType": "orderID", "existingEarningId": 181825, "saleAmount": "221.82", "approvedPayout": "88.73", "timestamp": "2025-07-09T22:39:20.411Z"}, {"scenario": 1, "scenarioName": "Duplicate Prevention", "orderId": "9634.6347.71573", "clickId": "CBCLKFBPRBGWVJB", "partner": "Impact", "reason": "Order ID 9634.6347.71573 already processed - Scenario 1 duplicate prevention", "duplicateType": "orderID", "existingEarningId": 181826, "saleAmount": "319.88", "approvedPayout": "22.39", "timestamp": "2025-07-09T22:39:23.843Z"}], "errors": [{"orderId": 1171766572, "partner": "Admitad", "error": "conversionData.adId?.trim is not a function. (In 'conversionData.adId?.trim()', 'conversionData.adId?.trim' is undefined)", "stack": "TypeError: conversionData.adId?.trim is not a function. (In 'conversionData.adId?.trim()', 'conversionData.adId?.trim' is undefined)\n    at checkForDuplicatesWithScenarios (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/DuplicateDetector.js:100:49)\n    at checkForDuplicatesWithScenarios (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/DuplicateDetector.js:13:40)\n    at processConversion (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:88:34)\n    at processConversion (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:79:26)\n    at processAllConversions (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:621:16)\n    at processTicksAndRejections (native:7:39)", "timestamp": "2025-07-09T22:39:26.640Z"}, {"orderId": "SYSTEM_ERROR", "clickId": "SYSTEM_ERROR", "partner": "SYSTEM", "errorMessage": "conversionData.adId?.trim is not a function. (In 'conversionData.adId?.trim()', 'conversionData.adId?.trim' is undefined)", "errorStack": "TypeError: conversionData.adId?.trim is not a function. (In 'conversionData.adId?.trim()', 'conversionData.adId?.trim' is undefined)\n    at checkForDuplicatesWithScenarios (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/DuplicateDetector.js:100:49)\n    at checkForDuplicatesWithScenarios (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/DuplicateDetector.js:13:40)\n    at processConversion (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:88:34)\n    at processConversion (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:79:26)\n    at processAllConversions (/home/<USER>/code/icb/ICB-Main-backend/services/clickTracking/ClickTrackingOrchestrator.js:621:16)\n    at processTicksAndRejections (native:7:39)", "saleAmount": 0, "approvedPayout": 0, "timestamp": "2025-07-09T22:39:26.640Z"}], "metadata": {"fileCreatedAt": "2025-07-09T22:39:27.466Z", "version": "2.0.0-enhanced", "totalRecords": {"newEarnings": 0, "skippedConversions": 2, "errors": 2}}}