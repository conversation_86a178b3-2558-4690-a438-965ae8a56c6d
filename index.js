import express from 'express'
import bodyParser from 'body-parser'
import cors from 'cors'
import swaggerUi from 'swagger-ui-express'
// Config and Middleware imports
import connectDatabase from './config/database.js'
import { errorMiddleware } from './middlewares/errorMiddleware.js'
import specs from './utils/swagger.js'

// Route imports
import adminRoutes from './routes/admin/admin.js'
import middlewareRoutes from './routes/admin/middleware.js'
import categoryRouter from './routes/admin/category.js'
import affiliationsRouter from './routes/admin/affiliations.js'
import giftCardRouter from './routes/admin/giftCard.js'
import icbGiftCardOrderRouter from './routes/icbGiftCardOrder.js'
import userRoutes from './routes/user/users.js'
import bannerRoutes from './routes/banners/banner.js'
import mobileStoryRoutes from './routes/banners/mobileStory.js'
import exampleRouter from './routes/example.js'
import offerRouter from './routes/offers/offer.js'
import onGoingSaleOfferRouter from './routes/offers/onGoingSales.js'
import clickRouters from './routes/clicks.js'
import missingCashbackRouter from './routes/payments/missingCashback.js'
import paymentRequestRouter from './routes/payments/paymentRequest.js'
import storeRoutes from './routes/stores/store.js'
import storeCategoryRouter from './routes/stores/storeCategory.js'
import trendingStoreRouter from './routes/stores/trending.js'
import termsAndPrivacyRouter from './routes/termsAndPrivacy.js'
import earningRouter from './routes/user/earnings.js'
import userReviews from './routes/user/reviews.js'
import personalInterestRouter from './routes/user/personalInterest.js'
import testimonialRouter from './routes/testimonial.js'
import quickAccessRouter from './routes/quickAccess.js'
// import tempRoutes from "./routes/tempRoutes.js";
import cron from 'node-cron'
// import {
// 	callAdmit,
// 	callAffalliances,
// 	// callAffle,
// 	// callClickoAffise,
// 	// callClickoTrackier,
// 	callImpact,
// 	// callRovers,
// 	// callShooglooTrackier,
// 	// callVcTrackier,
// 	createEarningObject,
// } from './services/affilitaionCron.js'
import { admitKeyChange } from './services/getAffiliationTokens.js'
import { removeInactiveOfferFromMeili } from './services/meilisSearchCron.js'
import { processAllAffiliateConversions } from './services/simplifiedAffiliationCron.js'

const app = express()

// CORS Configuration
const allowedOrigins = [
	'http://localhost:3000',
	'http://localhost:3001',
	'http://localhost:4000',
	'https://admin-panel.indiancashback.com',
	'https://icb-main-adminpanel.vercel.app',
	'http://***************:3000',
	'https://hoppscotch.io',
]
const corsOptions = {
	origin: (origin, callback) => {
		if (!origin || allowedOrigins.includes(origin)) {
			callback(null, true)
		} else {
			console.error('CORS rejected for:', origin) // More detailed error log
			callback(new Error('Not allowed by CORS'))
		}
	},
	credentials: true,
	allowedHeaders: [
		'Access-Control-Allow-Origin',
		'Content-Type',
		'Authorization',
	], // Include Authorization
	exposedHeaders: ['Authorization'], // Optionally, expose Authorization header if needed
}

app.use(cors(corsOptions))
app.set('trust proxy', 1)

// Body parser middleware setup
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }))
app.use(bodyParser.json({ limit: '30mb', extended: true }))
app.use(express.json({ limit: '50mb' }))
app.use(express.urlencoded({ limit: '30mb', extended: true }))

// Rate limiting
// const limiter = rateLimit({
//     windowMs: 15 * 60 * 1000, // 15 minutes
//     max: 100, // Limit each IP to 100 requests per window
//     standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
//     legacyHeaders: false, // Disable the `X-RateLimit-*` headers
// });
// app.use(limiter);

cron.schedule('0 0 * * *', () => {
	// execute every day at midnight
	console.log('-------Executes Every Day At Midnight   ------')
	admitKeyChange()
})

// This will execute every 1 hour
cron.schedule('0 * * * *', () => {
	removeInactiveOfferFromMeili()
})

// cron.schedule("*/10 * * * * *", () => {
//   removeInactiveOfferFromMeili();
// });

// Main processing - every 3 minutes
cron.schedule('*/5 * * * *', async () => {
	await processAllAffiliateConversions()
})
// This will execute every 10 minutes
// cron.schedule('*/10 * * * *', () => {
// 	callAdmit()
// 	// callClickoTrackier()
// 	// callRovers()
// 	// callShooglooTrackier()
// 	// callVcTrackier()
// 	callImpact()
// 	callAffalliances()
// 	// callClickoAffise()
// 	// callAffle()
// })

// Swagger Docs
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs))

// Routes
app.use('/api/admin', adminRoutes)
app.use('/api/middleware', middlewareRoutes)
app.use('/api/categories', categoryRouter)
app.use('/api/affiliations', affiliationsRouter)
app.use('/api/giftcards', giftCardRouter)
app.use('/api/icb-giftcard', icbGiftCardOrderRouter)
app.use('/api/offer', offerRouter)
app.use('/api/offer/on-going-sales', onGoingSaleOfferRouter)
app.use('/api/clicks', clickRouters)
app.use('/api/store', storeRoutes)
app.use('/api/store-categories', storeCategoryRouter)
app.use('/api/store/trending', trendingStoreRouter)
app.use('/api/reviews', userReviews)
app.use('/api/banners/stories', mobileStoryRoutes)
app.use('/api/banners/desktop', bannerRoutes)
app.use('/api/payments/missing', missingCashbackRouter)
app.use('/api/payments/request', paymentRequestRouter)
app.use('/api/users', userRoutes)
app.use('/api/earnings', earningRouter)
app.use('/api/personal-interest', personalInterestRouter)
app.use('/api/testimonials', testimonialRouter)
app.use('/api/terms-and-privacy', termsAndPrivacyRouter)
app.use('/api/quick-access', quickAccessRouter)
app.use('/api/test', exampleRouter)
// app.use("/api/temp", tempRoutes);

// Error handling middleware
app.use(errorMiddleware)

// Database connection and server startup
const PORT = process.env.PORT || 4000
connectDatabase()
	.then(() => {
		app.listen(PORT, () => {
			console.info(`Server running at http://localhost:${PORT}`)
		})
	})
	.catch(error => console.error('Database connection failed:', error.message))
