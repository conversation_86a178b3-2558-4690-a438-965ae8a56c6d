import { HttpException } from '../exceptions/httpException.js'
import { Clicks } from '../models/clicks.js'
import { User } from '../models/user/user.js'

export class ClickService {
	updateClickStatus = async (id, status) => {
		const click = await Clicks.findById(id)

		if (!click) {
			throw new HttpException(404, 'resource not found')
		}
		await Clicks.updateOne({ _id: id }, { $set: { status } })
		return click
	}

	// getAllClicks = async (query) => {
	//     const pageSize = Number(query.pageSize) || 10;
	//     const page = Number(query.page) || 1;
	//
	//     const keyword = {};
	//     if (query.status) {
	//         keyword.status = query.status;
	//     }
	//     if (query.store) {
	//         keyword.store = query.store;
	//     }
	//     if (query.affiliation) {
	//         keyword.affiliation = query.affiliation;
	//     }
	//
	//     if (query.userType === "guest") {
	//         keyword.user = { $exists: false }
	//     } else if (query.userType === "logged") {
	//         keyword.user = { $exists: true }; // Find clicks where the user field is not empty
	//     }
	//
	//     // if (query.search) {
	//     //     const searchRegex = new RegExp(query.search, 'i'); // 'i' for case insensitive
	//     //     keyword.$or = [
	//     //         { 'user.name': searchRegex },
	//     //         { 'user.uid': searchRegex }
	//     //     ];
	//     // }
	//     //
	//     console.log(query.search, "query search")
	//     const searchKeyword = query.search ? new RegExp(query.search, 'i') : null;
	//
	//
	//     const count = await Clicks.countDocuments({ ...keyword });
	//     const allClicks = await Clicks.find({ ...keyword })
	//         // .populate("user", "name email uid userNotes")
	//         .populate({
	//             path: 'user',
	//             select: 'name email uid userNotes',
	//             match: searchKeyword ? { $or: [{ name: searchKeyword }, { name: searchKeyword }] } : {}
	//         })
	//         .populate("offer", "title uid")
	//         .populate("store", "name")
	//         .populate("affiliation", "name")
	//         .sort({ _id: -1 })
	//         .limit(pageSize)
	//         .skip((page - 1) * pageSize);
	//
	//     console.log(allClicks, "violet")
	//
	//
	//     return {
	//         allClicks,
	//         page,
	//         pages: Math.ceil(count / pageSize),
	//         pageSize,
	//         search: query.search,
	//     };
	// };

	getAllClicks = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const matchStage = {}

		if (query.status) {
			matchStage.status = query.status
		}
		if (query.store) {
			matchStage.store = query.store
		}
		if (query.affiliation) {
			matchStage.affiliation = query.affiliation
		}

		if (query.userType === 'guest') {
			matchStage.user = { $exists: false }
		} else if (query.userType === 'logged') {
			matchStage.user = { $exists: true }
		}

		// Filter clicks by date range if fromDate and toDate are provided
		if (query.fromDate && query.toDate) {
			const fromDate = new Date(query.fromDate)
			const toDate = new Date(query.toDate)
			toDate.setHours(23, 59, 59, 999) // Set the time to the end of the day
			matchStage.createdAt = { $gte: fromDate, $lte: toDate }
		}

		const searchStage = {}
		if (query.search) {
			const searchRegex = new RegExp(query.search, 'i')

			searchStage.$or = [
				{ uid: Number.parseInt(query.search) },
				{ 'user.name': searchRegex },
				{ referenceId: searchRegex },
				{ 'user.name': searchRegex },
				{
					'user.uid': Number.parseInt(query.search),
				},
			]
		}

		const pipeline = [
			{ $match: matchStage },
			{
				$lookup: {
					from: 'users',
					localField: 'user',
					foreignField: '_id',
					as: 'user',
				},
			},
			// { $unwind: '$user' },
			{
				$addFields: {
					user: { $arrayElemAt: ['$user', 0] },
				},
			},
			{
				$match: {
					$or: [{ user: { $ne: null } }, { user: { $exists: false } }],
				},
			},
			{ $match: searchStage },
			{
				$lookup: {
					from: 'offers',
					localField: 'offer',
					foreignField: '_id',
					as: 'offer',
				},
			},
			{
				$addFields: {
					offer: { $arrayElemAt: ['$offer', 0] },
				},
			},
			{
				$match: {
					$or: [{ offer: { $ne: null } }, { offer: { $exists: false } }],
				},
			},

			{
				$lookup: {
					from: 'stores',
					localField: 'store',
					foreignField: '_id',
					as: 'store',
				},
			},
			//   { $unwind: "$store" },
			{
				$addFields: {
					store: { $arrayElemAt: ['$store', 0] },
				},
			},
			{
				$match: {
					$or: [{ store: { $ne: null } }, { store: { $exists: false } }],
				},
			},
			{
				$lookup: {
					from: 'affiliations',
					localField: 'affiliation',
					foreignField: '_id',
					as: 'affiliation',
				},
			},
			//   { $unwind: "$affiliation" },

			{
				$addFields: {
					affiliation: { $arrayElemAt: ['$affiliation', 0] },
				},
			},
			{
				$match: {
					$or: [
						{ affiliation: { $ne: null } },
						{ affiliation: { $exists: false } },
					],
				},
			},
			{ $sort: { _id: -1 } },
			{ $skip: (page - 1) * pageSize },
			{ $limit: pageSize },
		]

		const allClicks = await Clicks.aggregate(pipeline)

		const countPipeline = [
			{ $match: matchStage },
			{
				$lookup: {
					from: 'users',
					localField: 'user',
					foreignField: '_id',
					as: 'user',
				},
			},
			// { $unwind: '$user' },
			{
				$addFields: {
					user: { $arrayElemAt: ['$user', 0] },
				},
			},
			{
				$match: {
					$or: [{ user: { $ne: null } }, { user: { $exists: false } }],
				},
			},
			{ $match: searchStage },
			{ $count: 'count' },
		]

		const countResult = await Clicks.aggregate(countPipeline)
		const count = countResult.length > 0 ? countResult[0].count : 0

		return {
			allClicks,
			page,
			pages: Math.ceil(count / pageSize),
			pageSize,
			search: query.search,
		}
	}
	getClickDetails = async id => {
		const click = await Clicks.findById(id)
			.populate('user', 'name referral uid')
			.populate('offer', 'title uid')
			.populate('store', 'name uid')
			.populate('affiliation', 'name uid')
			.populate('storeCategory')

		const referral = await User.findById(click?.user?.referral, 'name')
		const response = {
			...click._doc,
			referral,
		}

		if (!click) {
			throw new HttpException(404, 'resource not found!')
		}

		return response
	}

	findClickByRefId = async refId => {
		const cashbackClick = await Clicks.findOne({ referenceId: refId }).populate(
			'user store',
		)

		if (!cashbackClick) {
			return null
		}

		return cashbackClick
	}
}
