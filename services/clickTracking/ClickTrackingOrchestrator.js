import { AffiliateDataFetcher } from './AffiliateDataFetcher.js'
import { ClickMatcher } from './ClickMatcher.js'
import { DuplicateDetector } from './DuplicateDetector.js'
import { CurrencyConverter } from './CurrencyConverter.js'
import { StoreValidator } from './StoreValidator.js'
import { EarningCreator } from './EarningCreator.js'
import { NotificationManager } from './NotificationManager.js'
import { PayoutValidator } from './PayoutValidator.js'
import { EnhancedErrorLogger } from './EnhancedErrorLogger.js'
import fs from 'node:fs'
import path from 'node:path'

/**
 * Main orchestrator for click tracking process with order-first approach
 */
export class ClickTrackingOrchestrator {
	constructor(services) {
		// Validate required services
		if (!services) {
			throw new Error('Services object is required')
		}

		const requiredServices = [
			'clickService',
			'earningService',
			'storeService',
			'userService',
			'adminService',
			'adminLogService',
		]
		for (const service of requiredServices) {
			if (!services[service]) {
				throw new Error(`Required service missing: ${service}`)
			}
		}

		// Initialize all modules
		this.fetcher = new AffiliateDataFetcher()
		this.matcher = new ClickMatcher(services.clickService)
		this.duplicateDetector = new DuplicateDetector(services.earningService)
		this.currencyConverter = new CurrencyConverter()
		this.storeValidator = new StoreValidator(services.storeService)
		this.earningCreator = new EarningCreator(
			services.earningService,
			services.clickService,
			services.adminService,
		)
		this.notificationManager = new NotificationManager(
			services.earningService,
			services.userService,
			services.storeService,
			services.adminLogService,
			services.adminService,
		)
		this.payoutValidator = new PayoutValidator()
		this.enhancedErrorLogger = new EnhancedErrorLogger(this.notificationManager)

		// Store services for access
		this.services = services

		// Processing metrics
		this.metrics = {
			conversionsProcessed: 0,
			earningsCreated: 0,
			duplicatesSkipped: 0,
			duplicatesByOrderId: 0,
			duplicatesByClickId: 0,
			errorsEncountered: 0,
			earningsWithClick: 0,
			earningsWithoutClick: 0,
			missingIdentifiers: 0,
			generatedOrderIds: 0,
			// Scenario-specific metrics
			scenario1_duplicatesPrevented: 0,
			scenario2_clickToEarning: 0,
			scenario3_standaloneEarning: 0,
			startTime: null,
			endTime: null,
		}

		// Tracking records for JSON export
		this.trackingRecords = {
			runInfo: {
				startTime: null,
				endTime: null,
				totalProcessed: 0,
				totalCreated: 0,
				totalSkipped: 0,
			},
			newlyTrackedEarnings: [],
			skippedConversions: [],
			errors: [],
		}
	}

	/**
	 * Process a single conversion with enhanced three-scenario approach
	 */
	async processConversion(conversionData) {
		// Validate conversion data
		if (!conversionData) {
			throw new Error('Conversion data is required')
		}

		// Ensure required properties exist with safe defaults
		const safeConversionData = {
			adId: conversionData.adId || null,
			partner: conversionData.partner || 'Unknown',
			affiliateInfo1: conversionData.affiliateInfo1 || null,
			affiliateInfo2: conversionData.affiliateInfo2 || null,
			saleAmount: conversionData.saleAmount || 0,
			approvedPayout: conversionData.approvedPayout || 0,
			currency: conversionData.currency || 'INR',
			datetime: conversionData.datetime || new Date().toISOString(),
			...conversionData,
		}

		try {
			console.log(
				`Processing conversion: ${safeConversionData.adId} from ${safeConversionData.partner}`,
			)
			this.metrics.conversionsProcessed++

			// Enhanced scenario-aware duplicate checking
			const scenarioCheck =
				await this.duplicateDetector.checkForDuplicatesWithScenarios(
					safeConversionData,
				)

			// Validate scenario check result
			if (!scenarioCheck) {
				throw new Error('Scenario check failed - no result returned')
			}

			// SCENARIO 1: Duplicate Prevention (Highest Priority)
			if (scenarioCheck.isDuplicate) {
				return this.handleScenario1_DuplicatePrevention(
					safeConversionData,
					scenarioCheck,
				)
			}

			// Currency conversion (common for all scenarios)
			const convertedData =
				await this.currencyConverter.convertConversionAmounts(
					safeConversionData,
				)

			// Route to appropriate scenario based on available data
			if (scenarioCheck.scenario === 2) {
				return this.handleScenario2_ClickToEarning(convertedData, scenarioCheck)
			}
			return this.handleScenario3_StandaloneEarning(
				convertedData,
				scenarioCheck,
			)
		} catch (error) {
			console.error(
				`Error processing conversion ${
					safeConversionData?.adId || conversionData?.adId || 'UNKNOWN'
				}:`,
				error,
			)
			this.trackingRecords.errors.push({
				orderId: safeConversionData?.adId || conversionData?.adId || 'N/A',
				partner:
					safeConversionData?.partner || conversionData?.partner || 'Unknown',
				error: error.message,
				stack: error.stack,
				timestamp: new Date().toISOString(),
			})
			throw error
		}
	}

	/**
	 * SCENARIO 1: Duplicate Prevention - Skip processing for duplicate orders
	 */
	async handleScenario1_DuplicatePrevention(conversionData, scenarioCheck) {
		console.log(
			`SCENARIO 1: Duplicate prevented for order ${conversionData.adId}`,
		)
		this.metrics.duplicatesSkipped++

		// Track scenario-specific metrics
		if (!this.metrics.scenario1_duplicatesPrevented) {
			this.metrics.scenario1_duplicatesPrevented = 0
		}
		this.metrics.scenario1_duplicatesPrevented++

		// Track duplicate detection method
		if (scenarioCheck.duplicateType === 'orderID') {
			this.metrics.duplicatesByOrderId++
		} else if (scenarioCheck.duplicateType === 'clickID') {
			this.metrics.duplicatesByClickId++
		}

		// Record skipped conversion with scenario information
		this.trackingRecords.skippedConversions.push({
			scenario: 1,
			scenarioName: 'Duplicate Prevention',
			orderId: conversionData.adId || 'N/A',
			clickId:
				conversionData.affiliateInfo1 || conversionData.affiliateInfo2 || 'N/A',
			partner: conversionData.partner,
			reason: scenarioCheck.reason,
			duplicateType: scenarioCheck.duplicateType,
			existingEarningId: scenarioCheck.existingEarning?.uid || 'N/A',
			saleAmount: conversionData.saleAmount || 0,
			approvedPayout: conversionData.approvedPayout || 0,
			timestamp: new Date().toISOString(),
		})

		return {
			scenario: 1,
			action: 'skipped',
			reason: 'duplicate_prevention',
			existingEarning: scenarioCheck.existingEarning,
		}
	}

	/**
	 * SCENARIO 2: Click-to-Earning Conversion - Process orders with click attribution
	 */
	async handleScenario2_ClickToEarning(convertedData, scenarioCheck) {
		try {
			// Validate inputs
			if (!convertedData) {
				throw new Error('Converted data is required for Scenario 2')
			}

			console.log(
				`SCENARIO 2: Processing click-to-earning conversion for order ${convertedData.adId}`,
			)

			// Find the click record
			const clickId =
				convertedData.affiliateInfo1 || convertedData.affiliateInfo2
			const clickData = await this.matcher.findMatchingClick(convertedData)

			if (!clickData) {
				console.log(
					`Click ${clickId || 'N/A'} not found - falling back to Scenario 3`,
				)
				return this.handleScenario3_StandaloneEarning(
					convertedData,
					scenarioCheck,
				)
			}

			// Validate click data
			const clickValidation = this.matcher.validateClick(clickData)
			if (!clickValidation.valid) {
				console.log(
					`Click validation failed: ${clickValidation.reason} - falling back to Scenario 3`,
				)
				return this.handleScenario3_StandaloneEarning(
					convertedData,
					scenarioCheck,
				)
			}

			// Check if click is already tracked
			if (clickData.status === 'tracked') {
				console.log(
					`Click ${clickData.referenceId} already tracked - falling back to Scenario 3`,
				)
				return this.handleScenario3_StandaloneEarning(
					convertedData,
					scenarioCheck,
				)
			}

			// Validate store settings
			const storeValidation = await this.storeValidator.validateStoreSettings(
				clickData.store,
			)
			if (!storeValidation.valid) {
				console.log(
					`Store validation failed: ${storeValidation.reason} - falling back to Scenario 3`,
				)
				return this.handleScenario3_StandaloneEarning(
					convertedData,
					scenarioCheck,
				)
			}

			// Create earning with click association
			const earning = await this.earningCreator.createEarningFromConversion(
				convertedData,
				clickData,
			)

			if (!earning) {
				throw new Error('Failed to create earning record in Scenario 2')
			}

			// Track scenario-specific metrics
			if (!this.metrics.scenario2_clickToEarning) {
				this.metrics.scenario2_clickToEarning = 0
			}
			this.metrics.scenario2_clickToEarning++
			this.metrics.earningsWithClick++

			// Record newly tracked earning with safe property access
			this.trackingRecords.newlyTrackedEarnings.push({
				scenario: 2,
				scenarioName: 'Click-to-Earning Conversion',
				earningId: earning?.uid || 'N/A',
				orderId: convertedData?.adId || 'N/A',
				clickId: clickId || 'N/A',
				partner: convertedData?.partner || 'Unknown',
				hasClickAssociation: true,
				clickReferenceId: clickData?.referenceId || 'N/A',
				userId: clickData?.user || 'N/A',
				storeId: clickData?.store || 'N/A',
				affiliationId: clickData?.affiliation || 'N/A',
				saleAmount: convertedData?.saleAmount || 0,
				approvedPayout: convertedData?.approvedPayout || 0,
				currency: convertedData?.currency || 'INR',
				originalCurrency:
					convertedData?.originalCurrency || convertedData?.currency || 'INR',
				originalSaleAmount:
					convertedData?.originalSaleAmount || convertedData?.saleAmount || 0,
				originalPayout:
					convertedData?.originalPayout || convertedData?.approvedPayout || 0,
				trackingTime: convertedData?.datetime || new Date().toISOString(),
				confirmDate: earning?.confirmDate || null,
				status: earning?.status || 'pending',
				isGenerated:
					String(convertedData?.adId || '').startsWith('AUTO-') || false,
				timestamp: new Date().toISOString(),
			})

			// Send notifications
			await this.notificationManager.sendTrackingNotifications(
				earning,
				clickData,
				convertedData,
			)

			console.log(
				`SCENARIO 2: Successfully created earning ${earning.uid} with click attribution`,
			)

			return {
				scenario: 2,
				action: 'created',
				earning,
				clickData,
			}
		} catch (error) {
			console.error('Error in Scenario 2 (Click-to-Earning):', error)
			// Fall back to Scenario 3 on error
			return this.handleScenario3_StandaloneEarning(
				convertedData,
				scenarioCheck,
			)
		}
	}

	/**
	 * SCENARIO 3: Standalone Earning Creation - Process orders without click attribution
	 */
	async handleScenario3_StandaloneEarning(convertedData, _scenarioCheck) {
		try {
			// Validate inputs
			if (!convertedData) {
				throw new Error('Converted data is required for Scenario 3')
			}

			console.log(
				`SCENARIO 3: Processing standalone earning for order ${convertedData.adId}`,
			)

			// Generate unique identifier if both order ID and click ID are missing
			const orderIdStr = String(convertedData.adId || '').trim()
			if (
				(!orderIdStr || orderIdStr === '') &&
				!convertedData.affiliateInfo1 &&
				!convertedData.affiliateInfo2
			) {
				const timestamp = Date.now()
				const randomSuffix = Math.random()
					.toString(36)
					.substring(2, 8)
					.toUpperCase()
				const partner = convertedData.partner || 'Unknown'
				convertedData.adId = `AUTO-${partner}-${timestamp}-${randomSuffix}`
				console.log(`Generated order ID for conversion: ${convertedData.adId}`)
				if (!this.metrics.generatedOrderIds) {
					this.metrics.generatedOrderIds = 0
				}
				this.metrics.generatedOrderIds++
			}

			// Create standalone earning
			const earning =
				await this.earningCreator.createStandaloneEarning(convertedData)

			if (!earning) {
				throw new Error(
					'Failed to create standalone earning record in Scenario 3',
				)
			}

			// Track scenario-specific metrics
			if (!this.metrics.scenario3_standaloneEarning) {
				this.metrics.scenario3_standaloneEarning = 0
			}
			this.metrics.scenario3_standaloneEarning++
			this.metrics.earningsWithoutClick++

			// Record newly tracked earning with safe property access
			this.trackingRecords.newlyTrackedEarnings.push({
				scenario: 3,
				scenarioName: 'Standalone Earning Creation',
				earningId: earning?.uid || 'N/A',
				orderId: convertedData?.adId || 'N/A',
				clickId:
					convertedData?.affiliateInfo1 ||
					convertedData?.affiliateInfo2 ||
					'N/A',
				partner: convertedData?.partner || 'Unknown',
				hasClickAssociation: false,
				clickReferenceId: 'N/A',
				userId: 'N/A',
				storeId: 'N/A',
				affiliationId: 'N/A',
				saleAmount: convertedData?.saleAmount || 0,
				approvedPayout: convertedData?.approvedPayout || 0,
				currency: convertedData?.currency || 'INR',
				originalCurrency:
					convertedData?.originalCurrency || convertedData?.currency || 'INR',
				originalSaleAmount:
					convertedData?.originalSaleAmount || convertedData?.saleAmount || 0,
				originalPayout:
					convertedData?.originalPayout || convertedData?.approvedPayout || 0,
				trackingTime: convertedData?.datetime || new Date().toISOString(),
				confirmDate: earning?.confirmDate || null,
				status: earning?.status || 'pending',
				isGenerated:
					String(convertedData?.adId || '').startsWith('AUTO-') || false,
				timestamp: new Date().toISOString(),
			})

			// Send notifications (without click data)
			await this.notificationManager.sendTrackingNotifications(
				earning,
				null,
				convertedData,
			)

			console.log(
				`SCENARIO 3: Successfully created standalone earning ${earning.uid}`,
			)

			return {
				scenario: 3,
				action: 'created',
				earning,
			}
		} catch (error) {
			console.error('Error in Scenario 3 (Standalone Earning):', error)
			this.metrics.errorsEncountered++

			// Record error
			this.trackingRecords.errors.push({
				scenario: 3,
				scenarioName: 'Standalone Earning Creation',
				orderId: convertedData.adId || 'N/A',
				clickId:
					convertedData.affiliateInfo1 || convertedData.affiliateInfo2 || 'N/A',
				partner: convertedData.partner,
				errorMessage: error.message,
				errorStack: error.stack,
				saleAmount: convertedData.saleAmount || 0,
				approvedPayout: convertedData.approvedPayout || 0,
				timestamp: new Date().toISOString(),
			})

			throw error
		}
	}

	/**
	 * Legacy method - maintained for backward compatibility
	 */
	async createEarningWithoutClick(convertedData) {
		return this.handleScenario3_StandaloneEarning(convertedData, {
			scenario: 3,
		})
	}

	/**
	 * Legacy method - maintained for backward compatibility
	 */
	async processConversionLegacy(conversionData) {
		try {
			console.log(
				`Processing conversion (legacy): ${conversionData.adId} from ${conversionData.partner}`,
			)
			this.metrics.conversionsProcessed++

			// 1. FIRST: Check for duplicates (order ID or click ID based)
			const duplicateCheck =
				await this.duplicateDetector.checkForDuplicates(conversionData)
			if (duplicateCheck.isDuplicate) {
				console.log(`Duplicate detected: ${duplicateCheck.reason}`)
				this.metrics.duplicatesSkipped++

				// Track duplicate detection method
				if (duplicateCheck.method === 'orderID') {
					this.metrics.duplicatesByOrderId++
				} else if (duplicateCheck.method === 'clickID') {
					this.metrics.duplicatesByClickId++
				}

				// Record skipped conversion
				this.trackingRecords.skippedConversions.push({
					orderId: conversionData.adId || 'N/A',
					clickId:
						conversionData.affiliateInfo1 ||
						conversionData.affiliateInfo2 ||
						'N/A',
					partner: conversionData.partner,
					reason: duplicateCheck.reason,
					detectionMethod: duplicateCheck.method || 'unknown',
					existingEarningId: duplicateCheck.existingEarning?.uid || 'N/A',
					saleAmount: conversionData.saleAmount || 0,
					approvedPayout: conversionData.approvedPayout || 0,
					timestamp: new Date().toISOString(),
				})
				return
			}

			// Track missing identifiers warning
			if (duplicateCheck.warning) {
				console.log(`Warning: ${duplicateCheck.warning}`)
				this.metrics.missingIdentifiers++
			}

			// 2. Convert currency if needed (early conversion for consistent data)
			const convertedData =
				await this.currencyConverter.convertConversionAmounts(conversionData)

			// 3. Try to find matching click (optional - for better tracking)
			const clickData = await this.matcher.findMatchingClick(conversionData)

			let earning
			if (clickData) {
				// Path A: Click found - full validation and tracking
				console.log(`Found matching click: ${clickData.referenceId}`)

				// Validate click data
				const clickValidation = this.matcher.validateClick(clickData)
				if (clickValidation.valid) {
					// Validate store settings
					const storeValidation =
						await this.storeValidator.validateStoreSettings(clickData.store)
					if (storeValidation.valid) {
						// Check if click is already tracked
						if (clickData.status === 'tracked') {
							console.log(
								`Click ${clickData.referenceId} already tracked - creating standalone earning`,
							)
							earning = await this.createEarningWithoutClick(convertedData)
							this.metrics.earningsWithoutClick++
						} else {
							// Create earning with click association
							earning = await this.earningCreator.createEarningFromConversion(
								convertedData,
								clickData,
							)
							this.metrics.earningsWithClick++
						}
					} else {
						console.log(
							`Store validation failed: ${storeValidation.reason} - creating standalone earning`,
						)
						earning = await this.createEarningWithoutClick(convertedData)
						this.metrics.earningsWithoutClick++
					}
				} else {
					console.log(
						`Click validation failed: ${clickValidation.reason} - creating standalone earning`,
					)
					earning = await this.createEarningWithoutClick(convertedData)
					this.metrics.earningsWithoutClick++
				}
			} else {
				// Path B: No click found - create standalone earning
				console.log(
					`No click found for conversion ${conversionData.adId} - creating standalone earning`,
				)
				earning = await this.createEarningWithoutClick(convertedData)
				this.metrics.earningsWithoutClick++
			}

			if (!earning) {
				throw new Error('Failed to create earning record')
			}

			// Record newly tracked earning
			this.trackingRecords.newlyTrackedEarnings.push({
				earningId: earning.uid,
				orderId: conversionData.adId || 'N/A',
				clickId:
					conversionData.affiliateInfo1 ||
					conversionData.affiliateInfo2 ||
					'N/A',
				partner: conversionData.partner,
				hasClickAssociation: clickData ? true : false,
				clickReferenceId: clickData?.referenceId || 'N/A',
				userId: clickData?.user || 'N/A',
				storeId: clickData?.store || 'N/A',
				affiliationId: clickData?.affiliation || 'N/A',
				saleAmount: convertedData.saleAmount || 0,
				approvedPayout: convertedData.approvedPayout || 0,
				currency: convertedData.currency || 'INR',
				originalCurrency:
					convertedData.originalCurrency || convertedData.currency || 'INR',
				originalSaleAmount:
					convertedData.originalSaleAmount || convertedData.saleAmount || 0,
				originalPayout:
					convertedData.originalPayout || convertedData.approvedPayout || 0,
				trackingTime: convertedData.datetime,
				confirmDate: earning.confirmDate,
				status: earning.status || 'pending',
				isGenerated: conversionData.adId?.startsWith('AUTO-') || false,
				timestamp: new Date().toISOString(),
			})

			// 4. Send notifications (with or without click data)
			await this.notificationManager.sendTrackingNotifications(
				earning,
				clickData || null,
				convertedData,
			)

			console.log(
				`Successfully processed conversion: ${conversionData.adId} -> Earning: ${earning.uid}`,
			)
		} catch (error) {
			console.error(
				`Error processing conversion ${conversionData.adId}:`,
				error,
			)
			this.trackingRecords.errors.push({
				orderId: conversionData.adId || 'N/A',
				partner: conversionData.partner,
				error: error.message,
				stack: error.stack,
				timestamp: new Date().toISOString(),
			})
			throw error
		}
	}

	/**
	 * Process all conversions from all partners
	 */
	async processAllConversions() {
		try {
			console.log(
				'Starting click tracking process with order-first approach...',
			)
			this.metrics.startTime = new Date()
			this.trackingRecords.runInfo.startTime =
				this.metrics.startTime.toISOString()

			// Fetch conversions from all partners
			const allConversions = await this.fetcher.fetchAllConversions(
				this.services.affiliationService,
			)

			if (allConversions.length === 0) {
				console.log('No conversions found to process')
				await this.createTrackingRecordFile()
				return
			}

			console.log(`Processing ${allConversions.length} conversions...`)

			// Process each conversion sequentially to avoid overwhelming the database
			for (const conversion of allConversions) {
				await this.processConversion(conversion)

				// 500ms delay to prevent database overload (optimized for 3-minute cron frequency)
				await this.delay(500)
			}

			this.metrics.endTime = new Date()
			this.trackingRecords.runInfo.endTime = this.metrics.endTime.toISOString()
			this.trackingRecords.runInfo.totalProcessed =
				this.metrics.conversionsProcessed
			this.trackingRecords.runInfo.totalCreated = this.metrics.earningsCreated
			this.trackingRecords.runInfo.totalSkipped = this.metrics.duplicatesSkipped

			this.logProcessingSummary()
			await this.createTrackingRecordFile()

			console.log('Click tracking process completed')
		} catch (error) {
			console.error('Error in click tracking process:', error)
			this.metrics.errorsEncountered++

			// Record critical error
			this.trackingRecords.errors.push({
				orderId: 'SYSTEM_ERROR',
				clickId: 'SYSTEM_ERROR',
				partner: 'SYSTEM',
				errorMessage: error.message,
				errorStack: error.stack,
				saleAmount: 0,
				approvedPayout: 0,
				timestamp: new Date().toISOString(),
			})

			// Send critical error notification
			await this.notificationManager.sendErrorNotification(
				error,
				'Main processing loop',
			)

			// Still create tracking file even on error
			await this.createTrackingRecordFile()
		}
	}

	/**
	 * Process conversions for a specific partner
	 */
	async processPartnerConversions(partner) {
		try {
			console.log(`Processing ${partner} conversions...`)

			let conversions = []

			switch (partner.toLowerCase()) {
				case 'impact': {
					conversions = await this.fetcher.fetchImpactConversions()
					break
				}
				case 'affalliances': {
					conversions = await this.fetcher.fetchAffalliancesConversions()
					break
				}
				case 'admitad': {
					conversions = await this.fetcher.fetchAdmitadConversions(
						this.services.affiliationService,
					)
					break
				}
				default: {
					console.log(`Unknown partner: ${partner}`)
					return
				}
			}

			console.log(`Found ${conversions.length} ${partner} conversions`)

			for (const conversion of conversions) {
				await this.processConversion(conversion)

				// 500ms delay between each conversion processing (optimized for 3-minute cron frequency)
				await this.delay(500)
			}

			console.log(`Completed ${partner} processing`)
		} catch (error) {
			console.error(`Error processing ${partner} conversions:`, error)
		}
	}

	/**
	 * Get processing metrics
	 */
	getMetrics() {
		const duration =
			this.metrics.endTime && this.metrics.startTime
				? this.metrics.endTime - this.metrics.startTime
				: 0

		return {
			...this.metrics,
			processingDurationMs: duration,
			processingDurationSeconds: Math.round(duration / 1000),
			successRate:
				this.metrics.conversionsProcessed > 0
					? `${(
							(this.metrics.earningsCreated /
								this.metrics.conversionsProcessed) *
							100
						).toFixed(2)}%`
					: '0%',
			clickMatchRate:
				this.metrics.earningsCreated > 0
					? `${(
							(this.metrics.earningsWithClick / this.metrics.earningsCreated) *
							100
						).toFixed(2)}%`
					: '0%',
		}
	}

	/**
	 * Log processing summary
	 */
	logProcessingSummary() {
		const metrics = this.getMetrics()

		console.log('Click Tracking Summary (Enhanced Order-First Approach):')
		console.log(`   Conversions Processed: ${metrics.conversionsProcessed}`)
		console.log(`   Earnings Created: ${metrics.earningsCreated}`)
		console.log(`   - With Click Association: ${metrics.earningsWithClick}`)
		console.log(
			`   - Without Click Association: ${metrics.earningsWithoutClick}`,
		)
		console.log(`   Duplicates Skipped: ${metrics.duplicatesSkipped}`)
		console.log(`   - Duplicates by Order ID: ${metrics.duplicatesByOrderId}`)
		console.log(`   - Duplicates by Click ID: ${metrics.duplicatesByClickId}`)
		console.log(`   Missing Identifiers: ${metrics.missingIdentifiers}`)
		console.log(`   Generated Order IDs: ${metrics.generatedOrderIds}`)
		console.log(`   Errors Encountered: ${metrics.errorsEncountered}`)
		console.log(`   Success Rate: ${metrics.successRate}`)
		console.log(`   Click Match Rate: ${metrics.clickMatchRate}`)
		console.log(`   Processing Time: ${metrics.processingDurationSeconds}s`)
		console.log('   Delay Between Conversions: 500ms')
	}

	/**
	 * Reset metrics for new processing cycle
	 */
	resetMetrics() {
		this.metrics = {
			conversionsProcessed: 0,
			earningsCreated: 0,
			duplicatesSkipped: 0,
			duplicatesByOrderId: 0,
			duplicatesByClickId: 0,
			errorsEncountered: 0,
			earningsWithClick: 0,
			earningsWithoutClick: 0,
			missingIdentifiers: 0,
			generatedOrderIds: 0,
			// Scenario-specific metrics
			scenario1_duplicatesPrevented: 0,
			scenario2_clickToEarning: 0,
			scenario3_standaloneEarning: 0,
			startTime: null,
			endTime: null,
		}
	}

	/**
	 * Create tracking record JSON file
	 */
	async createTrackingRecordFile() {
		try {
			const fileName = 'click-track-last-record.json'
			const filePath = path.join(process.cwd(), fileName)

			// Prepare comprehensive tracking data
			const trackingData = {
				runInfo: {
					...this.trackingRecords.runInfo,
					runId: `RUN-${Date.now()}`,
					processingDurationMs:
						this.metrics.endTime && this.metrics.startTime
							? this.metrics.endTime - this.metrics.startTime
							: 0,
					processingDurationSeconds:
						this.metrics.endTime && this.metrics.startTime
							? Math.round(
									(this.metrics.endTime - this.metrics.startTime) / 1000,
								)
							: 0,
				},
				summary: {
					conversionsProcessed: this.metrics.conversionsProcessed,
					earningsCreated: this.metrics.earningsCreated,
					earningsWithClick: this.metrics.earningsWithClick,
					earningsWithoutClick: this.metrics.earningsWithoutClick,
					duplicatesSkipped: this.metrics.duplicatesSkipped,
					duplicatesByOrderId: this.metrics.duplicatesByOrderId,
					duplicatesByClickId: this.metrics.duplicatesByClickId,
					missingIdentifiers: this.metrics.missingIdentifiers,
					generatedOrderIds: this.metrics.generatedOrderIds,
					errorsEncountered: this.metrics.errorsEncountered,
					successRate:
						this.metrics.conversionsProcessed > 0
							? `${(
									(this.metrics.earningsCreated /
										this.metrics.conversionsProcessed) *
									100
								).toFixed(2)}%`
							: '0%',
					clickMatchRate:
						this.metrics.earningsCreated > 0
							? `${(
									(this.metrics.earningsWithClick /
										this.metrics.earningsCreated) *
									100
								).toFixed(2)}%`
							: '0%',
				},
				newlyTrackedEarnings: this.trackingRecords.newlyTrackedEarnings,
				skippedConversions: this.trackingRecords.skippedConversions,
				errors: this.trackingRecords.errors,
				metadata: {
					fileCreatedAt: new Date().toISOString(),
					version: '2.0.0-enhanced',
					totalRecords: {
						newEarnings: this.trackingRecords.newlyTrackedEarnings.length,
						skippedConversions: this.trackingRecords.skippedConversions.length,
						errors: this.trackingRecords.errors.length,
					},
				},
			}

			// Write to JSON file (replace existing)
			await fs.promises.writeFile(
				filePath,
				JSON.stringify(trackingData, null, 2),
				'utf8',
			)

			console.log(`Tracking record saved to: ${fileName}`)
			console.log(
				`Records: ${trackingData.metadata.totalRecords.newEarnings} earnings, ${trackingData.metadata.totalRecords.skippedConversions} skipped, ${trackingData.metadata.totalRecords.errors} errors`,
			)
		} catch (error) {
			console.error('Error creating tracking record file:', error)
		}
	}

	/**
	 * Utility delay function
	 */
	delay(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}
}
