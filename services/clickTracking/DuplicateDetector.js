/**
 * Detects and prevents duplicate order processing with scenario-aware approach
 */
export class DuplicateDetector {
	constructor(earningService) {
		this.earningService = earningService
	}

	/**
	 * Scenario-aware duplicate check for three-scenario workflow
	 * Returns detailed information for scenario decision-making
	 */
	async checkForDuplicatesWithScenarios(conversionData) {
		try {
			// Extract order ID and click ID with proper type handling
			const orderId = conversionData.adId
				? String(conversionData.adId).trim()
				: null
			const clickId =
				conversionData.affiliateInfo1 || conversionData.affiliateInfo2

			const result = {
				isDuplicate: false,
				scenario: null,
				orderIdExists: <PERSON><PERSON><PERSON>(orderId),
				clickIdExists: <PERSON><PERSON><PERSON>(clickId),
				existingEarning: null,
				duplicateType: null,
				recommendedAction: null,
				reason: null,
			}

			// SCENARIO 1 CHECK: Order ID duplicate detection (highest priority)
			if (orderId) {
				const existingEarningByOrder =
					await this.earningService.getEarningByOrderId(orderId)
				if (existingEarningByOrder) {
					console.log(
						`SCENARIO 1: Order ${orderId} already exists as earning ${existingEarningByOrder.uid}`,
					)
					return {
						...result,
						isDuplicate: true,
						scenario: 1,
						duplicateType: 'orderID',
						existingEarning: existingEarningByOrder,
						recommendedAction: 'skip_processing',
						reason: `Order ID ${orderId} already processed - Scenario 1 duplicate prevention`,
					}
				}
			}

			// SCENARIO 2/3 PREPARATION: Check click ID for scenario routing
			if (clickId) {
				const existingEarningByClick =
					await this.earningService.getEarningByClickId(clickId)
				if (existingEarningByClick) {
					console.log(
						`Click ${clickId} already processed as earning ${existingEarningByClick.uid}`,
					)
					return {
						...result,
						isDuplicate: true,
						scenario: 2,
						duplicateType: 'clickID',
						existingEarning: existingEarningByClick,
						recommendedAction: 'skip_processing',
						reason: `Click ID ${clickId} already processed - preventing duplicate click earning`,
					}
				}
			}

			// Determine recommended scenario based on available data
			if (orderId && clickId) {
				result.recommendedAction = 'proceed_scenario_2'
				result.scenario = 2
				result.reason =
					'Both Order ID and Click ID available - proceed with click-to-earning conversion'
			} else if (orderId) {
				result.recommendedAction = 'proceed_scenario_3'
				result.scenario = 3
				result.reason =
					'Only Order ID available - proceed with standalone earning creation'
			} else if (clickId) {
				result.recommendedAction = 'proceed_scenario_3'
				result.scenario = 3
				result.reason =
					'Only Click ID available - proceed with standalone earning creation'
			} else {
				result.recommendedAction = 'proceed_scenario_3'
				result.scenario = 3
				result.reason =
					'No identifiers available - will generate auto ID for standalone earning'
			}

			return result
		} catch (error) {
			console.error('Error in scenario-aware duplicate check:', error)
			return {
				isDuplicate: false,
				scenario: 3,
				orderIdExists: Boolean(
					conversionData.adId ? String(conversionData.adId).trim() : null,
				),
				clickIdExists: Boolean(
					conversionData.affiliateInfo1 || conversionData.affiliateInfo2,
				),
				existingEarning: null,
				duplicateType: null,
				recommendedAction: 'proceed_scenario_3',
				reason:
					'Error during duplicate check - defaulting to standalone earning creation',
				error: error.message,
			}
		}
	}

	/**
	 * Legacy comprehensive duplicate check - maintained for backward compatibility
	 */
	async checkForDuplicates(conversionData) {
		const scenarioResult =
			await this.checkForDuplicatesWithScenarios(conversionData)

		return {
			isDuplicate: scenarioResult.isDuplicate,
			reason: scenarioResult.reason,
			existingEarning: scenarioResult.existingEarning,
			method: scenarioResult.duplicateType,
			warning:
				scenarioResult.orderIdExists || scenarioResult.clickIdExists
					? null
					: 'Missing both Order ID and Click ID',
			error: scenarioResult.error,
		}
	}

	/**
	 * Check if order already exists (legacy method)
	 */
	async checkOrderExists(orderId) {
		try {
			const existingEarning =
				await this.earningService.getEarningByOrderId(orderId)

			if (existingEarning) {
				console.log(
					`Order ${orderId} already exists as earning ${existingEarning.uid}`,
				)
				return existingEarning
			}

			return null
		} catch (error) {
			console.error('Error checking order existence:', error)
			// Return null on error to avoid blocking legitimate orders
			return null
		}
	}

	/**
	 * Check if order already exists for the store (legacy method)
	 */
	async isDuplicateOrder(orderId, storeId) {
		try {
			const existingEarning =
				await this.earningService.getEarningByOrderIdAndStore(orderId, storeId)

			if (existingEarning) {
				console.log(`Duplicate order detected: ${orderId} for store ${storeId}`)
				return true
			}

			return false
		} catch (error) {
			console.error('Error checking duplicates:', error)
			// Return false on error to avoid blocking legitimate orders
			return false
		}
	}

	/**
	 * Check if click is already tracked
	 */
	isClickAlreadyTracked(clickData) {
		if (clickData.status === 'tracked') {
			console.log(`Click already tracked: ${clickData.referenceId}`)
			return true
		}
		return false
	}

	/**
	 * Comprehensive duplicate check (legacy method - now simplified)
	 */
	async performDuplicateChecks(conversionData, clickData) {
		// Check if click is already tracked
		if (this.isClickAlreadyTracked(clickData)) {
			return { isDuplicate: true, reason: 'Click already tracked' }
		}

		// Check if order already exists
		const isDuplicateOrder = await this.isDuplicateOrder(
			conversionData.adId,
			clickData.store,
		)
		if (isDuplicateOrder) {
			return { isDuplicate: true, reason: 'Duplicate order ID' }
		}

		return { isDuplicate: false }
	}
}
