import { PayoutValidator } from './PayoutValidator.js'
import { EnhancedErrorLogger } from './EnhancedErrorLogger.js'

/**
 * Creates earning records from validated conversions
 */
export class EarningCreator {
	constructor(earningService, clickService, adminService) {
		this.earningService = earningService
		this.clickService = clickService
		this.adminService = adminService
		this.payoutValidator = new PayoutValidator()
		this.enhancedErrorLogger = new EnhancedErrorLogger(null) // No notification manager in this context
	}

	/**
	 * Calculate confirmation date (3 months from tracking)
	 */
	calculateConfirmDate(datetime) {
		const confirmDate = new Date(datetime)
		confirmDate.setMonth(confirmDate.getMonth() + 3)
		return confirmDate
	}

	/**
	 * Create earning payload from conversion data
	 */
	async createEarningPayload(conversionData, clickData) {
		try {
			const autoAdmin = await this.adminService.getAdminDetailsWithEmail(
				'<EMAIL>',
			)

			return {
				referenceId: clickData.referenceId,
				trackingTime: conversionData.datetime,
				orderUniqueId: conversionData.adId,
				cashbackAmount: 0, // Will be calculated later by admin
				orderCount: 1,
				amountGot: Number.parseFloat(conversionData.approvedPayout),
				saleAmount: Number.parseFloat(conversionData.saleAmount),
				confirmDate: this.calculateConfirmDate(conversionData.datetime),
				offer: clickData.offer,
				user: clickData.user,
				click: clickData._id,
				store: clickData.store,
				affiliation: clickData.affiliation,
				notes: conversionData.notes || '',
				otherInfo: conversionData.advertiserInfo || '',
				autoUpdated: true,
				createdBy: autoAdmin._id,
				clickDate: conversionData.clickDate,
				purchaseDate: conversionData.purchaseDate,
				status: 'pending', // Initial status
				isShareAndEarn: clickData?.link ? true : false,
			}
		} catch (error) {
			console.error('Error creating earning payload:', error)
			throw error
		}
	}

	/**
	 * Create earning record and update click status
	 */
	async createEarningFromConversion(conversionData, clickData) {
		try {
			console.log(`🔄 Creating earning for order ${conversionData.adId}`)

			// Create earning payload
			const earningPayload = await this.createEarningPayload(
				conversionData,
				clickData,
			)

			// Create earning record
			const earning = await this.earningService.createEarnings(earningPayload)

			if (!earning) {
				throw new Error('Failed to create earning record')
			}

			// Update click status to tracked
			await this.clickService.updateClickStatus(earning.click, 'tracked')

			console.log(
				`✅ Created earning ${earning.uid} for click ${clickData.referenceId}`,
			)
			return earning
		} catch (error) {
			console.error('Error creating earning from conversion:', error)
			throw error
		}
	}

	/**
	 * Create standalone earning without click association
	 */
	async createStandaloneEarning(conversionData) {
		try {
			console.log(
				`Creating standalone earning for order ${conversionData.adId}`,
			)

			const autoAdmin = await this.adminService.getAdminDetailsWithEmail(
				'<EMAIL>',
			)

			const earningPayload = {
				referenceId: `STANDALONE-${conversionData.adId}`,
				trackingTime: conversionData.datetime,
				orderUniqueId: conversionData.adId,
				cashbackAmount: 0, // Will be calculated later by admin
				orderCount: 1,
				amountGot: Number.parseFloat(conversionData.approvedPayout),
				saleAmount: Number.parseFloat(conversionData.saleAmount),
				confirmDate: this.calculateConfirmDate(conversionData.datetime),
				offer: null, // No offer association
				user: null, // No user association
				click: null, // No click association
				store: null, // No store association
				affiliation: null, // No affiliation association
				notes: `Standalone earning from ${conversionData.partner}. ${
					conversionData.notes || ''
				}`,
				otherInfo: conversionData.advertiserInfo || '',
				autoUpdated: true,
				createdBy: autoAdmin._id,
				clickDate: conversionData.clickDate,
				purchaseDate: conversionData.purchaseDate,
				status: 'pending', // Initial status
				partner: conversionData.partner,
				originalCurrency:
					conversionData.originalCurrency || conversionData.currency,
				originalSaleAmount:
					conversionData.originalSaleAmount || conversionData.saleAmount,
				originalPayout:
					conversionData.originalPayout || conversionData.approvedPayout,
			}

			// Create earning record
			const earning = await this.earningService.createEarnings(earningPayload)

			if (!earning) {
				throw new Error('Failed to create standalone earning record')
			}

			console.log(
				`Created standalone earning ${earning.uid} for order ${conversionData.adId}`,
			)
			return earning
		} catch (error) {
			console.error('Error creating standalone earning:', error)
			throw error
		}
	}

	/**
	 * Validate earning creation requirements
	 */
	validateEarningRequirements(conversionData, clickData) {
		const errors = []

		if (!conversionData.adId) {
			errors.push('Missing order ID')
		}

		if (!conversionData.approvedPayout || conversionData.approvedPayout <= 0) {
			errors.push('Invalid payout amount')
		}

		if (!clickData.referenceId) {
			errors.push('Missing click reference ID')
		}

		if (!clickData.user) {
			errors.push('Missing user association')
		}

		if (!clickData.store) {
			errors.push('Missing store association')
		}

		if (errors.length > 0) {
			console.log(`Earning validation failed: ${errors.join(', ')}`)
			return { valid: false, errors }
		}

		return { valid: true }
	}

	/**
	 * Validate standalone earning requirements with enhanced diagnostics
	 */
	validateStandaloneEarningRequirements(conversionData) {
		// Use enhanced payout validator for comprehensive validation
		const validationResult =
			this.payoutValidator.validateStandalonePayout(conversionData)

		if (!validationResult.valid) {
			// Log detailed diagnostic information
			this.enhancedErrorLogger.logPayoutValidationError(
				validationResult,
				conversionData,
				{ errors: [] }, // Minimal tracking records for this context
			)

			// Return legacy format for backward compatibility
			const legacyErrors = validationResult.diagnostics.errors.map(
				error => `${error.field}: ${error.details}`,
			)

			return {
				valid: false,
				errors: legacyErrors,
				enhancedDiagnostics: validationResult, // Include enhanced data for future use
			}
		}

		// Log successful validation
		this.enhancedErrorLogger.logValidationSuccess(
			validationResult,
			conversionData,
		)

		return {
			valid: true,
			enhancedDiagnostics: validationResult, // Include enhanced data for future use
		}
	}
}
